import { connect } from 'cloudflare:sockets';

const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct', // Optional direct relayip relaysocks Default direct
        retryMode: 'relayip', // Optional relayip relaysocks Default relayip
    },

    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },

    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        // Example:  user:pass@host:port  or  host:port
        socks: 'web5.serv00.com:13668',
        // socks: 'user:<EMAIL>:13668',
    },

    api: {
        addresses: 'https://rtmainalraw.pages.dev/az/index.txt', // https://raw.almain126.changeip.org/az/index.txt
        addresses2: 'https://rtmainalraw.pages.dev/az/main.txt',
        directTemplate: 'https://rtmainalraw.pages.dev/az/templatedirect.txt',
        globalTemplate: 'https://rtmainalraw.pages.dev/az/templateglobal.txt',
    },

    misc: {
        subName: 'myMain',
    }
};

const WS_STATES = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
};

export default {
    async fetch(request, env, ctx) {
        try {
            const { CONNECT_MODE, RETRY_MODE, USER_GUID, USER_PASS, USER_SHA224, RELAY_IP, RELAY_SOCKS, API_TXT, API_TXT_2, API_DIRECT_TEMPLATE_URL, API_GLOBAL_TEMPLATE_URL } = env;
            globalControllerConfig.connectMode = (CONNECT_MODE || globalSessionConfig.connect.connectMode).toLowerCase();
            globalControllerConfig.retryMode = (RETRY_MODE || globalSessionConfig.connect.retryMode).toLowerCase();

            globalSessionConfig.user.id = USER_GUID || globalSessionConfig.user.id;
            globalSessionConfig.user.pass = USER_PASS || globalSessionConfig.user.pass;
            globalSessionConfig.user.sha224 = USER_SHA224 || globalSessionConfig.user.sha224;
            globalSessionConfig.relay.ip = RELAY_IP || globalSessionConfig.relay.ip;
            globalSessionConfig.relay.socks = RELAY_SOCKS || globalSessionConfig.relay.socks;
            globalSessionConfig.api.addresses = API_TXT || globalSessionConfig.api.addresses; // API_ADDRESSES_URL
            globalSessionConfig.api.addresses2 = API_TXT_2 || globalSessionConfig.api.addresses2; // API_ADDRESSES_URL_2
            globalSessionConfig.api.directTemplate = API_DIRECT_TEMPLATE_URL || globalSessionConfig.api.directTemplate;
            globalSessionConfig.api.globalTemplate = API_GLOBAL_TEMPLATE_URL || globalSessionConfig.api.globalTemplate;

            const userAgent = (request.headers.get('User-Agent') || 'null').toLowerCase();
            const url = new URL(request.url);
            const upgradeHeader = request.headers.get('Upgrade');
            if (!upgradeHeader || upgradeHeader !== 'websocket') {
                switch (url.pathname) {
                    case '/':
                        // return new Response(JSON.stringify(request.cf), { status: 200 });
                        // return new Response(JSON.stringify(request.cf, null, 2), { status: 200 });
                        // return new Response(JSON.stringify(request.cf, null, 4), { status: 200 });
                        return new Response(null, { status: 204 });
                    case `/a`: {
                        return new Response(null, { status: 204 });
                    }
                    case `/z`: {
                        const newResponse = new Response(null, { status: 204 });
                        console.log('Status:', newResponse.status);
                        return newResponse;
                    }
                    case `/aazz`: {
                        try {
                            const inputString = await fetchRemoteData(globalSessionConfig.api.addresses);
                            // const inputTemplate = await fetchRemoteData(globalSessionConfig.api.directTemplate);
                            const inputTemplate = await fetchRemoteData(globalSessionConfig.api.globalTemplate);

                            const getConfig = (type, tls) =>
                                url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64')
                                    ? (() => {
                                        const configs = generateConfigs(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'));
                                        if (type === 'both') return btoa(configs.protocolP0 + '\n' + configs.protocolP1);
                                        if (type === 'p0') return btoa(configs.protocolP0);
                                        if (type === 'p1') return btoa(configs.protocolP1);
                                    })()
                                    : getCustomConfig(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'), type, inputTemplate);

                            const configs = {
                                both: getConfig('both', true),
                                p0: getConfig('p0', true),
                                p1: getConfig('p1', true),
                                bothNotls: getConfig('both', false),
                                p0Notls: getConfig('p0', false),
                                p1Notls: getConfig('p1', false)
                            };

                            const configMappings = [
                                { param: 'both', config: configs.both },
                                { param: 'az', config: configs.p0 },
                                { param: 'za', config: configs.p1 },
                                { param: 'both-notls', config: configs.bothNotls },
                                { param: 'az-notls', config: configs.p0Notls },
                                { param: 'za-notls', config: configs.p1Notls }
                            ];

                            const getResponseConfig = (isMozilla) => ({
                                status: 200,
                                headers: {
                                    "Content-Type": "text/plain;charset=utf-8",
                                    ...(isMozilla ? {} : {
                                        "Content-Disposition": `attachment; filename=${globalSessionConfig.misc.subName}; filename*=utf-8''${encodeURIComponent(globalSessionConfig.misc.subName)}`,
                                        "Profile-Update-Interval": "6"
                                    })
                                }
                            });

                            const isMozilla = userAgent && userAgent.includes('mozilla');
                            const prefixes = ['b64', 'base64'];

                            for (const prefix of prefixes) {
                                for (const { param, config } of configMappings) {
                                    const fullParam = `${prefix}${param}`;
                                    if (url.searchParams.has(fullParam)) {
                                        return new Response(config, getResponseConfig(isMozilla));
                                    }
                                }
                            }

                            for (const { param, config } of configMappings) {
                                if (url.searchParams.has(param)) {
                                    return new Response(config, getResponseConfig(isMozilla));
                                }
                            }

                            // Default case
                            // return new Response(configMappings[0].config, getResponseConfig(isMozilla));
                            // Default case
                            const isB64Request = url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64');
                            const defaultConfig = isB64Request
                                ? getConfig('both', true)
                                : configMappings[0].config;

                            return new Response(defaultConfig, getResponseConfig(isMozilla));

                        } catch (error) {
                            return new Response(error.message, { status: 400 });
                        }
                    }

                    default:
                        return new Response('Not found', { status: 404 });
                    // return new Response('Expected Upgrade: websocket', { status: 426 });
                }
                // } else {
            } else if (upgradeHeader === 'websocket') {
                if (url.searchParams.has('relayip')) {
                    globalSessionConfig.relay.ip = url.searchParams.get('relayip') || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/relayip=')) {
                    globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1]?.trim() || globalSessionConfig.relay.ip.trim();
                    // globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1].split('/')[0]?.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.searchParams.has('socks')) {
                    globalSessionConfig.relay.socks = url.searchParams.get('socks') || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/socks=')) {
                    globalSessionConfig.relay.socks = url.pathname.split('/socks=')[1]?.trim() || globalSessionConfig.relay.socks.trim();
                    // globalSessionConfig.relay.socks = url.pathname.split('/socks=/i')[1].split('/')[0]?.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                }

                const [relayIp, relayPort] = globalSessionConfig.relay.ip.split(':');
                globalSessionConfig.relay.ip = relayIp;
                if (relayPort) globalSessionConfig.relay.port = relayPort;

                // const handleSessionA = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const handleSessionA = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const handleSessionA = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                const HANDLER_CHOICE = 4;
                const handlerConfigs = {
                    1: { sessionA: handleSession, sessionZ: handleSession },
                    2: { sessionA: processSession, sessionZ: processSession },
                    3: { sessionA: handleSession, sessionZ: processSession },
                    4: { sessionA: sessionHandle, sessionZ: sessionHandle },
                    5: { sessionA: sessionProcess, sessionZ: sessionProcess },
                    6: { sessionA: sessionHandle, sessionZ: sessionProcess },
                };
                const config = handlerConfigs[HANDLER_CHOICE];
                const handleSessionA = (request, env, ctx) => config.sessionA(request, env, ctx, globalControllerConfig.targetProtocolType0);
                const handleSessionZ = (request, env, ctx) => config.sessionZ(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const pathHandlers = {
                //     [globalControllerConfig.targetPathType1]: handleSessionZ,
                //     [globalControllerConfig.targetPathType0]: handleSessionA
                // }
                // const pathType = url.pathname.split('/')[1];
                // const handler = pathHandlers[pathType] || handleSessionA
                // // return handler(request, env, ctx)
                // return await handler(request, env, ctx)
                // // return (pathHandlers[pathType] ? pathHandlers[pathType] : handleSessionA)(request, env, ctx)
                // // return await (pathHandlers[pathType] ? pathHandlers[pathType] : handleSessionA)(request, env, ctx)

                let handler;
                const pathType = url.pathname.split('/')[1];
                switch (pathType) {
                    case globalControllerConfig.targetPathType1:
                        handler = handleSessionZ;
                        break;
                    case globalControllerConfig.targetPathType0:
                        handler = handleSessionA;
                        break;
                    default:
                        handler = handleSessionA;
                }

                // return handler(request, env, ctx)
                return await handler(request, env, ctx)
            }
        } catch (error) {
            return new Response(`fetch Error: ${error.message}`, { status: 500 });
            // return new Response(error.toString());
            // return new Response(error.toString(), { status: 400 });
            // return new Response(error.message, { status: 400 });
            // return new Response(error.stack || error.message, { status: 400 });
        }
    },
};

export async function sessionHandle(request, env, ctx, protocolMode) {
    // Global error handler to catch any remaining uncaught exceptions
    const originalOnError = globalThis.onerror;
    const originalOnUnhandledRejection = globalThis.onunhandledrejection;

    let sessionCleanedUp = false;
    const sessionCleanup = () => {
        if (sessionCleanedUp) return;
        sessionCleanedUp = true;
        // Restore original handlers
        globalThis.onerror = originalOnError;
        globalThis.onunhandledrejection = originalOnUnhandledRejection;
    };

    // Install session-specific error handlers
    globalThis.onerror = (message, source, lineno, colno, error) => {
        sessionCleanup();
        return originalOnError ? originalOnError(message, source, lineno, colno, error) : false;
    };

    globalThis.onunhandledrejection = (event) => {
        sessionCleanup();
        return originalOnUnhandledRejection ? originalOnUnhandledRejection(event) : false;
    };

    const [client, server] = Object.values(new WebSocketPair());
    // const { 0: client, 1: server } = Object.values(new WebSocketPair())
    server.accept();
    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";

    /* TCP → WS downstream pump modes: optional values | pipeTo | pipeThrough | reader | forawaitof */
    let downstreamMode = "reader";   // ← Change here to switch modes  // TCP→WS downstream pump implementation

    /* Backlog processing modes: optional values | shift | forof | while | forloop */
    let backlogMode = "forloop";   // ← Change here to switch backlog processing modes (forloop=fastest)

    // Pre-allocate array capacity to reduce dynamic expansion overhead
    const backlog = new Array();
    let tcpInterface = null;
    let reader = null;
    let writer = null;
    let started = false;
    let isCleaningUp = false;  // Prevent duplicate cleanup operations

    if (earlyHeader) {
        server.addEventListener("message", e => {
            try {
                if (writer) {
                    writer.write(e.data).catch(() => { cleanup(); });
                } else {
                    backlog.push(e.data);
                }
            } catch (e) {
                cleanup();
            }
        });

        if (!started) {
            started = true;
            try {
                // Wrap in Promise to catch all async errors
                Promise.resolve(run(decodeBase64Url(earlyHeader)))
                    .catch(() => { cleanup(); });
            } catch (error) {
                cleanup();
            }
        }
    } else {
        server.addEventListener("message", e => {
            try {
                // Wrap async call to prevent uncaught exceptions
                Promise.resolve(handleFrame(e.data))
                    .catch(() => { cleanup(); });
            } catch (error) {
                cleanup();
            }
        });
    }

    // Unified cleanup function to prevent duplicate operations
    const cleanup = () => {
        if (isCleaningUp) return;
        isCleaningUp = true;
        started = false;

        // Cleanup session error handlers
        sessionCleanup();

        // Cleanup promises for non-blocking resource cleanup
        const cleanupPromises = new Array(2);

        cleanupPromises[0] = Promise.resolve().then(() => {
            try {
                if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                    tcpInterface.close();
                }
            } catch (e) { /* Ignore cleanup errors */ }
        }).catch(() => { /* Ignore all cleanup errors */ });

        cleanupPromises[1] = Promise.resolve().then(() => {
            try {
                if (server && server.close instanceof Function &&
                    (server.readyState !== WS_STATES.CLOSING && server.readyState !== WS_STATES.CLOSED)) {
                    server.close(1000);
                }
            } catch (e) { /* Ignore cleanup errors */ }
        }).catch(() => { /* Ignore all cleanup errors */ });

        // Non-blocking cleanup with full error suppression
        Promise.allSettled(cleanupPromises).catch(() => { /* Ignore all errors */ });
    };

    server.addEventListener("close", () => {
        cleanup();
    });

    server.addEventListener("error", () => {
        cleanup();
    });

    async function handleFrame(buf) {
        if (isCleaningUp) return;  // Skip processing if cleaning up

        try {
            if (!started) {
                started = true;
                await run(buf);
            } else if (!writer) {
                backlog.push(buf);
            } else {
                // Non-blocking write with comprehensive error handling
                writer.write(buf).catch(() => { cleanup(); });
            }
        } catch (e) {
            cleanup();
            // Don't re-throw to prevent uncaught exceptions
        }
    }

    async function run(firstBuf) {
        try {
            let header;
            switch (protocolMode) {
                case globalControllerConfig.targetProtocolType0:
                    header = await parseProtocolHeaderType0(firstBuf, server);
                    break;
                case globalControllerConfig.targetProtocolType1:
                    header = await parseProtocolHeaderType1(firstBuf, server);
                    break;
                default:
                    throw new Error("Unknown protocol mode");
            }

            try {
                tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
            } catch {
                try {
                    tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                } catch (e) {
                    started = false;
                    throw e;
                }
                // tcpInterface = await Promise.any([
                //     dial(header, globalControllerConfig.retryMode, protocolMode),
                //     dial(header, globalControllerConfig.retryMode, protocolMode)
                // ]);
            }

            writer = tcpInterface.writable.getWriter();

            if (header.rawClientData) {
                try {
                    await writer.write(header.rawClientData);
                } catch (e) {
                    // Log but don't throw - this might be expected in some cases
                    // started = false;
                    // throw e;
                }
            }

            /* flush queued frames during connection establishment; refresh queued frames in the backlog */
            if (backlog.length > 0) {
                switch (backlogMode) {
                    case "shift": {
                        // Option 1: while + shift (performance: poor, memory: gradually released)
                        while (backlog.length) {
                            try {
                                await writer.write(backlog.shift());
                            } catch (e) {
                                cleanup();
                                throw e;
                            }
                        }
                        break;
                    }
                    case "forof": {
                        // Option 2: for-of loop (performance: medium, readability: good)
                        for (const i of backlog) {
                            try {
                                await writer.write(i);
                            } catch (e) {
                                cleanup();
                                throw e;
                            }
                        }
                        backlog.length = 0;
                        break;
                    }
                    case "while": {
                        // Option 3: while + index increment (performance: good, complexity: medium)
                        let i = 0;
                        while (i < backlog.length) {
                            try {
                                await writer.write(backlog[i++]);
                            } catch (e) {
                                cleanup();
                                throw e;
                            }
                        }
                        backlog.length = 0;
                        break;
                    }
                    case "forloop": {
                        // Option 4: for loop (performance: best, readability: clear) - CURRENT
                        for (let i = 0; i < backlog.length; i++) {
                            try {
                                await writer.write(backlog[i]);
                            } catch (e) {
                                cleanup();
                                throw e;
                            }
                        }
                        backlog.length = 0;
                        break;
                    }
                    default:
                        throw new Error(`Unknown backlogMode: ${backlogMode}`);
                }
            }

            /*  Start downstream pump */
            let pumpTask;
            switch (downstreamMode) {
                case "pipeTo": {
                    pumpTask = pumpPipeTo(tcpInterface.readable, server);
                    break;
                }
                case "pipeThrough": {
                    pumpTask = pumpPipeThrough(tcpInterface.readable, server);
                    break;
                }
                case "reader": {
                    pumpTask = pumpReader(tcpInterface.readable, server);
                    break;
                }
                case "forawaitof": {
                    pumpTask = pumpForAwaitOf(tcpInterface.readable, server);
                    break;
                }
                default:
                    throw new Error(`Unknown downstreamMode: ${downstreamMode}`);
            }

            // Enhanced error handling for pump operations
            try {
                await pumpTask;  // Block until the pump completes or throws
            } catch (e) {
                // Ignore expected WebSocket close errors to reduce scriptThrewException
                if (!(e instanceof TypeError && e.message?.includes('WebSocket'))) {
                    throw e;
                }
            }

        } catch (e) {
            cleanup();
            // Don't re-throw to prevent uncaught exceptions in event handlers
        } finally {
            // Final cleanup is handled by the unified cleanup function
            cleanup();
        }
    }

    // Ensure session cleanup happens when response is returned
    setTimeout(() => sessionCleanup(), 0);
    return new Response(null, { status: 101, webSocket: client });
}

export async function sessionProcess(request, env, ctx, protocolMode) {
    // const [client, server] = Object.values(new WebSocketPair());
    const { 0: client, 1: server } = Object.values(new WebSocketPair());
    server.accept();

    /* ======================= Configuration section ======================= */
    const earlyHeader = request.headers.get("sec-websocket-protocol") || "";

    /** WS → TCP upstream ingress modes: optional values | transform | readable */
    let ingressMode = "transform";   // ← Change here to switch modes // WS→TCP upstream implementation

    /** WS → TCP upstream processing modes: optional values | pipeTo | pipeThrough | reader | forawaitof */
    let upstreamMode = "pipeTo";   // ← Change here to switch upstream processing modes

    /* TCP → WS downstream pump modes: optional values | pipeTo | pipeThrough | reader | forawaitof */
    let downstreamMode = "reader";   // ← Change here to switch modes  // TCP→WS downstream pump implementation

    /* ========= Build the upstream Readable according to ingressMode ========= */
    let upstreamReadable;    // Unified reference to the upstream stream
    let holdWriter = null;   // Writer reference in transform mode
    let tcpInterface = null;
    let reader = null;
    let writer = null;

    if (ingressMode === "transform") {
        /* ----- Approach 1: TransformStream buffering ----- */
        const hold = new TransformStream();
        upstreamReadable = hold.readable;
        holdWriter = hold.writable.getWriter();

        if (earlyHeader) {
            try {
                holdWriter.write(decodeBase64Url(earlyHeader)).catch(() => { });
            } catch (e) { }
        }
        server.addEventListener("message", e => {
            try {
                holdWriter.write(e.data);
            } catch (e) {
                // Ignore write errors to prevent uncaught exceptions
            }
        });
        // server.addEventListener("close", (event) => {
        //     try {
        //         holdWriter.close();
        //     } catch (e) {
        //         // Ignore close errors
        //     }
        // });
        // server.addEventListener("error", (event) => {
        //     try {
        //         holdWriter.abort(event);
        //     } catch (e) {
        //         // Ignore abort errors
        //     }
        // });
        server.addEventListener("close", (event) => {
            try {
                if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                    tcpInterface.close();
                }
            } catch (e) { }
        });

        server.addEventListener("error", (event) => {
            try {
                if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                    tcpInterface.close();
                }
            } catch (e) { }
            try {
                if (server && server.close instanceof Function &&
                    (server.readyState === WS_STATES.OPEN || server.readyState === WS_STATES.CONNECTING)) {
                    server.close(1013);
                }
            } catch (e) { }
        });

    } else if (ingressMode === "readable") {
        /* ----- Approach 2: custom ReadableStream ----- */
        upstreamReadable = new ReadableStream({
            start(controller) {
                if (earlyHeader) {
                    try {
                        controller.enqueue(decodeBase64Url(earlyHeader));
                    } catch (e) { }
                }
                server.addEventListener("message", e => {
                    try {
                        controller.enqueue(e.data);
                    } catch (e) {
                        // Ignore enqueue errors to prevent uncaught exceptions
                    }
                });
                server.addEventListener("close", (event) => {
                    try {
                        controller.close();
                    } catch (e) {
                        // Ignore close errors
                    }
                });
                server.addEventListener("error", (event) => {
                    try {
                        controller.error(event);
                    } catch (e) {
                        // Ignore error propagation issues
                    }
                });
            },
        });
    }

    (async () => {
        try {
            // /* Read the first frame for protocol parsing */
            // const peekReader = upstreamReadable.getReader();
            // const { value: firstBuf } = await peekReader.read();
            // peekReader.releaseLock();

            // /* Parse the protocol header according to protocolMode */
            // let header;
            // switch (protocolMode) {
            //     case globalControllerConfig.targetProtocolType0:
            //         header = await parseProtocolHeaderType0(firstBuf, server);
            //         break;
            //     case globalControllerConfig.targetProtocolType1:
            //         header = await parseProtocolHeaderType1(firstBuf, server);
            //         break;
            //     default:
            //         throw new Error("Unknown protocol mode");
            // }

            /* modes: optional values | pipeTo | pipeThrough | reader */
            let header = await parseHeader(upstreamReadable, server, protocolMode, 'pipeTo');

            /* Sequential dialing: connectMode → retryMode */
            try {
                tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
            } catch {
                try {
                    tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                } catch (e) {
                    throw e;
                }
                // tcpInterface = await Promise.any([
                //     dial(header, globalControllerConfig.retryMode, protocolMode),
                //     dial(header, globalControllerConfig.retryMode, protocolMode)
                // ]);
            }

            writer = tcpInterface.writable.getWriter();

            /* ========== WS → TCP upstream processing performance ranking ========== */
            switch (upstreamMode) {
                case "pipeTo": {
                    upstreamReadable.pipeTo(
                        new WritableStream({
                            async write(chunk, controller) {
                                try {
                                    await writer.write(chunk);
                                } catch (e) {
                                    if (e instanceof TypeError) controller.error();
                                    throw e;
                                }
                            },
                            async close() {
                                try {
                                    await writer.close();
                                } catch (e) {
                                    // Ignore close errors
                                }
                            }
                        })
                    ).catch(() => { /* Ignore background errors */ });  // Run in the background
                    break;
                }
                case "pipeThrough": {
                    upstreamReadable
                        .pipeThrough(new TransformStream({
                            async transform(chunk, controller) {
                                try {
                                    await writer.write(chunk);
                                } catch (e) {
                                    if (e instanceof TypeError) controller.error();
                                    throw e;
                                }
                            },
                            async flush() {
                                try {
                                    await writer.close();
                                } catch (e) {
                                    // Ignore close errors
                                }
                            }
                        }))
                        .pipeTo(new WritableStream())
                        .catch(() => { /* Ignore background errors */ });  // Run in the background
                    break;
                }
                case "reader": {
                    (async () => {
                        const reader = upstreamReadable.getReader();
                        try {
                            while (true) {
                                const { value: chunk, done } = await reader.read();
                                if (done) break;
                                try {
                                    await writer.write(chunk);
                                } catch (e) {
                                    if (e instanceof TypeError) break;
                                    throw e;
                                }
                            }
                        } finally {
                            reader.releaseLock();
                            await writer.close();
                        }
                    })().catch(() => { /* Ignore background errors */ });  // Run in the background
                    break;
                }
                case "forawaitof": {
                    (async () => {
                        try {
                            for await (const chunk of upstreamReadable) {
                                try {
                                    await writer.write(chunk);
                                } catch (e) {
                                    if (e instanceof TypeError) break;
                                    throw e;
                                }
                            }
                        } finally {
                            await writer.close();
                        }
                    })().catch(() => { /* Ignore background errors */ });  // Run in the background
                    break;
                }
                default:
                    throw new Error(`Unknown upstreamMode: ${upstreamMode}`);
            }

            if (header.rawClientData) {
                try {
                    await writer.write(header.rawClientData);
                } catch (e) {
                    // Log but don't throw - this might be expected in some cases
                }
            }

            /*  Start downstream pump */
            let pumpTask;
            switch (downstreamMode) {
                case "pipeTo": {
                    pumpTask = pumpPipeTo(tcpInterface.readable, server);
                    break;
                }
                case "pipeThrough": {
                    pumpTask = pumpPipeThrough(tcpInterface.readable, server);
                    break;
                }
                case "reader": {
                    pumpTask = pumpReader(tcpInterface.readable, server);
                    break;
                }
                case "forawaitof": {
                    pumpTask = pumpForAwaitOf(tcpInterface.readable, server);
                    break;
                }
                default:
                    throw new Error(`Unknown downstreamMode: ${downstreamMode}`);
            }
            await pumpTask;  // Block until the pump completes or throws

        } catch (e) {
            try {
                if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                    tcpInterface.close();
                }
            } catch (e) { }
        } finally {
            try {
                if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                    tcpInterface.close();
                }
            } catch (e) { }

            try {
                if (server && server.close instanceof Function &&
                    (server.readyState !== WS_STATES.CLOSING && server.readyState !== WS_STATES.CLOSED)) {
                    server.close(1000);
                }
            } catch (e) { }
        }
    })().catch((e) => {
        try {
            if (tcpInterface && 'close' in tcpInterface && tcpInterface.close instanceof Function) {
                tcpInterface.close();
            }
        } catch (e) { }

        try {
            if (server && server.close instanceof Function &&
                (server.readyState !== WS_STATES.CLOSING && server.readyState !== WS_STATES.CLOSED)) {
                server.close(1000);
            }
        } catch (e) { }
    });

    return new Response(null, { status: 101, webSocket: client });
}

/* Single-dial wrapper */
async function dial(header, mode, protocolMode) {
    const iface = await createConnection(header, mode, protocolMode);
    await iface.opened; // Return after the connection is fully established
    return iface;
}

async function pumpPipeTo(src, ws) { // pipeTo approach
    await src.pipeTo(new WritableStream({
        write(chunk, controller) {
            // Return a Promise so that pipeTo can detect back-pressure and errors
            try {
                return ws.send(chunk);
            } catch (e) {
                if (e instanceof TypeError) controller.error();
                throw e;
            }
        }
    }));
}

async function pumpPipeThrough(src, ws) { // pipeThrough + pipeTo approach
    await src.pipeThrough(new TransformStream({
        transform(chunk, controller) {
            // Return a Promise so that pipeTo can detect back-pressure and errors
            try {
                return ws.send(chunk);
            } catch (e) {
                if (e instanceof TypeError) controller.error();
                throw e;
            }
        },
    })).pipeTo(new WritableStream());
}

async function pumpReader(src, ws) { // getReader manual loop
    const reader = src.getReader();
    try {
        // while (true) {
        for (; ;) {
            const { value: chunk, done } = await reader.read();
            if (done) break;
            try {
                ws.send(chunk);
            } catch (e) {
                // If the WS has been closed, ws.send throws a TypeError
                if (e instanceof TypeError) break;
                throw e;
            }
        }
    } finally {
        // reader.releaseLock();
        // NEW: Null-value guard to avoid throwing when not yet initialized
        reader?.releaseLock();
    }
}

async function pumpForAwaitOf(src, ws) { // for-await-of loop
    for await (const chunk of src) {
        try {
            ws.send(chunk);
        } catch (e) {
            if (e instanceof TypeError) break;
            throw e;
        }
    }
}

async function handleSession(request, env, ctx, protocolMode) {
    const [wsClient, wsServer] = Object.values(new WebSocketPair())
    // const { 0: wsClient, 1: wsServer } = new WebSocketPair()
    try {
        const client = createWsClient(request, wsClient, wsServer)
        wsServer.accept()
        handleClient(client, protocolMode, 'stream') // stream stream7 reader reader4 forawaitof forawaitof4
        return client.response
    } catch (error) {
        console.log(`WebSocket client error: ${error.message}`)
        return new Response('Connection error', { status: 500 })
    }
}
async function processSession(request, env, ctx, protocolMode) {
    const [wsClient, wsServer] = Object.values(new WebSocketPair())
    // const { 0: wsClient, 1: wsServer } = new WebSocketPair()
    try {
        const client = createWsClient(request, wsClient, wsServer)
        wsServer.accept()
        handleClient(client, protocolMode, 'forawaitof') // stream stream7 reader reader4 forawaitof forawaitof4
        return client.response
    } catch (error) {
        console.log(`WebSocket client error: ${error.message}`)
        return new Response('Connection error', { status: 500 })
    }
}

async function handleClient(client, protocolMode, streamMode, header, remote) {
    const abortController = new AbortController()

    try {
        /* modes: optional values | pipeTo | pipeThrough | reader */
        header = await parseHeader(client.readable, client.server, protocolMode, 'pipeTo')
        try {
            remote = await createConnection(header, globalControllerConfig.connectMode, protocolMode);
            await remote.opened;
        } catch {
            try {
                remote = await createConnection(header, globalControllerConfig.retryMode, protocolMode);
            } catch (error) {
            }
        }

        const combinedSignal = AbortSignal.any([
            client.signal,
            abortController.signal
        ]);

        combinedSignal.addEventListener('abort', () => {
            try { client?.close(1013) } catch { }
            try { remote?.close() } catch { }
            // console.log('Connection closed')
        }, { once: true })

        if (header.rawClientData) {
            const writer = remote.writable.getWriter()

            // await writer.ready
            // await writer.write(header.rawClientData)
            // writer.releaseLock()

            try {
                // await writer.ready
                await writer.write(header.rawClientData)
            } finally {
                writer.releaseLock()
            }

            // await writer.ready
            //     .then(() => writer.write(header.rawClientData))
            //     .finally(() => writer.releaseLock())
        }

        async function pipeStream(sourceStream, destinationStream, combinedSignal, direction) {
            sourceStream
                .pipeTo(destinationStream, { signal: combinedSignal })
                .catch(error => {
                    if (!combinedSignal.aborted) {
                        console.log(`${direction} error:`, error)
                    }
                })
                .finally(() => {
                    // abortController.abort()
                });
        }
        async function pipeStreamWithWritable(sourceStream, destinationStream, combinedSignal, direction) {
            sourceStream
                .pipeTo(
                    new WritableStream({
                        async write(chunk) {
                            await destinationStream.write(chunk)
                        },
                        async close() {
                            // try { await destinationStream.close() } catch { }
                            try { destinationStream.releaseLock() } catch { }
                        },
                    }), { signal: combinedSignal })
                .catch(error => {
                    if (!combinedSignal.aborted) {
                        console.log(`${direction} error:`, error)
                    }
                })
                .finally(() => {
                    // abortController.abort()
                });
        }
        async function pipeRemoteToClient(remote, client, combinedSignal) {
            remote.readable
                .pipeTo(
                    new WritableStream({
                        write(chunk) {
                            if (!combinedSignal.aborted) {
                                client.server.send(chunk)
                            }
                        }
                    }),
                    { signal: combinedSignal }
                )
                .catch(error => {
                    if (!combinedSignal.aborted) {
                        console.log(`Download error using pipeTo:`, error)
                    }
                })
                .finally(() => {
                    // abortController.abort()
                });
        }

        async function pipeStreamWithReader(reader, writer, combinedSignal, direction) {
            try {
                while (!combinedSignal.aborted) {
                    const { done, value } = await reader.read()
                    if (done) break;
                    await writer.write(value)
                }
            } catch (error) {
                if (!combinedSignal.aborted) {
                    console.log(`${direction} error:`, error)
                }
            } finally {
                try { reader.releaseLock() } catch { }
                try { writer.releaseLock() } catch { }
                // abortController.abort()
            }
        }
        async function pipeRemoteWithReader(remote, client, combinedSignal) {
            const remoteReader = remote.readable.getReader()
            try {
                while (!combinedSignal.aborted) {
                    const { done, value } = await remoteReader.read()
                    if (done) break;
                    client.server.send(value)
                }
            } catch (error) {
                if (!combinedSignal.aborted) {
                    console.log(`Download error using getReader:`, error)
                }
            } finally {
                try { remoteReader.releaseLock() } catch { }
                // abortController.abort()
            }
        }

        async function pipeStreamWithForAwait(sourceStream, destinationStream, combinedSignal, direction) {
            const writer = destinationStream.getWriter()
            try {
                for await (const chunk of sourceStream) {
                    if (combinedSignal.aborted) break
                    await writer.write(chunk)
                }
            } catch (error) {
                if (!combinedSignal.aborted) {
                    console.log(`${direction} error:`, error)
                }
            } finally {
                // try { await writer.close() } catch { }
                try { writer.releaseLock() } catch { }
                // abortController.abort()
            }
        }
        async function pipeRemoteWithForAwait(remote, client, combinedSignal) {
            try {
                for await (const chunk of remote.readable) {
                    if (combinedSignal.aborted) break
                    client.server.send(chunk)
                }
            } catch (error) {
                if (!combinedSignal.aborted) {
                    console.log(`Download error using for-await-of:`, error);
                }
            } finally {
                // abortController.abort()
            }
        }

        const modeHandlers = {
            'stream': async () => {
                pipeStream(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeStream(remote.readable, client.writable, combinedSignal, 'download')
            }, // Automatic stream processing using pipeTo
            'stream1': async () => {
                pipeStream(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeRemoteToClient(remote, client, combinedSignal)
            }, // Automatic stream processing using pipeTo
            'stream2': async () => {
                pipeStream(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeRemoteWithReader(remote, client, combinedSignal)
            }, // Automatic stream processing using pipeTo + getReader() + read()
            'stream3': async () => {
                pipeStream(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeRemoteWithForAwait(remote, client, combinedSignal)
            }, // Automatic stream processing using pipeTo + for-await-of()
            'stream4': async () => {
                pipeStreamWithWritable(client.readable, remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeRemoteToClient(remote, client, combinedSignal)
            }, // Automatic stream processing using pipeTo()
            'stream5': async () => {
                pipeStreamWithWritable(client.readable, remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeStreamWithWritable(remote.readable, client.writable.getWriter(), combinedSignal, 'Download')
            }, // Automatic stream processing using pipeTo()
            'stream6': async () => {
                pipeStreamWithWritable(client.readable, remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeRemoteWithReader(remote, client, combinedSignal)
            }, // Automatic stream processing using pipeTo() + getReader() + read()
            'stream7': async () => {
                pipeStreamWithWritable(client.readable, remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeRemoteWithForAwait(remote, client, combinedSignal)
            }, // Automatic stream processing using pipeTo() + for-await-of()
            'reader': async () => {
                pipeStreamWithReader(client.readable.getReader(), remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeRemoteWithReader(remote, client, combinedSignal)
            }, // Manual stream processing using getReader() + read()
            'reader1': async () => {
                pipeStreamWithReader(client.readable.getReader(), remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeStreamWithReader(remote.readable.getReader(), client.writable.getWriter(), combinedSignal, 'Download')
            }, // Manual stream processing using getReader() + read()
            'reader2': async () => {
                pipeStreamWithReader(client.readable.getReader(), remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeRemoteWithForAwait(remote, client, combinedSignal)
            }, // Manual stream processing using getReader() + read() + for-await-of()
            'reader3': async () => {
                pipeStreamWithReader(client.readable.getReader(), remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeStreamWithForAwait(remote.readable, client.writable, combinedSignal, 'Download')
            }, // Manual stream processing using getReader() + read() + for-await-of()
            'reader4': async () => {
                pipeStreamWithReader(client.readable.getReader(), remote.writable.getWriter(), combinedSignal, 'Upload')
                pipeRemoteToClient(remote, client, combinedSignal)
            }, // Manual stream processing using getReader() + read() + pipeTo()
            'forawaitof': async () => {
                pipeStreamWithForAwait(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeRemoteWithForAwait(remote, client, combinedSignal)
            }, // Manual stream processing using for-await-of()
            'forawaitof1': async () => {
                pipeStreamWithForAwait(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeStreamWithForAwait(remote.readable, client.writable, combinedSignal, 'Download')
            }, // Manual stream processing using for-await-of()
            'forawaitof2': async () => {
                pipeStreamWithForAwait(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeRemoteWithReader(remote, client, combinedSignal)
            }, // Manual stream processing using for-await-of() + getReader() + read()
            'forawaitof3': async () => {
                pipeStreamWithForAwait(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeStreamWithReader(remote.readable.getReader(), client.writable.getWriter(), combinedSignal, 'Download')
            }, // Manual stream processing using for-await-of() + getReader() + read()
            'forawaitof4': async () => {
                pipeStreamWithForAwait(client.readable, remote.writable, combinedSignal, 'Upload')
                pipeRemoteToClient(remote, client, combinedSignal)
            }, // Manual stream processing using for-await-of() + pipeTo()
            'default': () => {
                throw new Error(`Unsupported mode: ${streamMode}`)
            }
        };
        await (modeHandlers[streamMode]
            ? modeHandlers[streamMode]()
            : Promise.reject(new Error(`Unsupported mode: ${streamMode}`)))

        remote?.closed
            .then(() => {
                abortController.abort()
            })
            .catch((error) => {
                abortController.abort()
            })
        // remote.closed.then(() => { abortController.abort() })
        // remote.closed.catch(() => { abortController.abort() })
        // remote.closed.finally(() => { abortController.abort() })

    } catch (error) {
        // console.log(`handle client error: ${error.message}`)
        try { abortController.abort() } catch { }
        return new Response(`handle client error: ${error.message}`, { status: 400 })
    } finally {
    }
}

export const parseHeader = async (src, ws, protocolMode, mode = 'reader', result = null) => {
    const ac = new AbortController();
    // const handleHeader = {
    //     [globalControllerConfig.targetProtocolType0]: parseProtocolHeaderType0,
    //     [globalControllerConfig.targetProtocolType1]: parseProtocolHeaderType1,
    // }[protocolMode];
    // if (!handleHeader) {
    //     return new Response('Unknown protocol mode', { status: 400 });
    // }
    let handleHeader;
    switch (protocolMode) {
        case globalControllerConfig.targetProtocolType0:
            handleHeader = parseProtocolHeaderType0;
            break;
        case globalControllerConfig.targetProtocolType1:
            handleHeader = parseProtocolHeaderType1;
            break;
        default:
            return new Response('Unknown protocol mode', { status: 400 });
    }

    try {
        switch (mode) {
            case 'reader': {
                const reader = src.getReader();
                try {
                    const { value: chunk, done } = await reader.read();
                    result = await handleHeader(chunk, ws);
                } finally {
                    reader.releaseLock();
                }
                break;
            }
            case 'pipeTo': {
                try {
                    await src.pipeTo(
                        new WritableStream({
                            async write(chunk, controller) {
                                try {
                                    result = await handleHeader(chunk, ws);
                                } catch (headerError) {
                                    // If header parsing fails, set error result
                                    result = new Response(`Header parse error: ${headerError.message}`, { status: 400 });
                                } finally {
                                    ac.abort()
                                }
                            }
                        }),
                        { signal: ac.signal, preventCancel: true }
                    );
                } catch (error) {
                    // return new Response(`parse header error: ${error.message}`, { status: 400 });
                    ac.abort();
                }
                break;
            }
            case 'pipeThrough': {
                try {
                    await src
                        .pipeThrough(
                            new TransformStream({
                                async transform(chunk, controller) {
                                    try {
                                        result = await handleHeader(chunk, ws);
                                    } catch (headerError) {
                                        // If header parsing fails, set error result
                                        result = new Response(`Header parse error: ${headerError.message}`, { status: 400 });
                                    } finally {
                                        ac.abort();
                                    }
                                },
                            }),
                            { signal: ac.signal, preventCancel: true }
                        )
                        .pipeTo(new WritableStream(), { signal: ac.signal, preventCancel: true });
                } catch (error) {
                    // return new Response(`parse header error: ${error.message}`, { status: 400 });
                    ac.abort();
                }
                break;
            }
            default:
                throw new Error(`Unknown mode: ${mode}`);
        }
    } finally {
        ac.abort();
    }

    return result || new Response('Client closed before sending header', { status: 400 })
};

function createWsClient(request, client, server) {
    const abortCtrl = new AbortController()
    const earlyDataHeader = request.headers.get('sec-websocket-protocol') || ''

    return {
        client,
        server,
        readable: new ReadableStream({
            start(controller) {
                if (earlyDataHeader) {
                    try {
                        controller.enqueue(decodeBase64Url(earlyDataHeader))
                    } catch {
                        abortCtrl.abort()
                    }
                }

                server.addEventListener('message', ({ data }) => {
                    try {
                        controller.enqueue(data)
                    } catch {
                        abortCtrl.abort()
                    }
                }, { signal: abortCtrl.signal });
                server.addEventListener('close', () => {
                    try {
                        controller.close()
                    } catch {
                    } finally {
                        abortCtrl.abort()
                    }
                }, { signal: abortCtrl.signal });
                server.addEventListener('error', (error) => {
                    try {
                        controller.error(error)
                        // controller.close()
                    } catch {
                    } finally {
                        abortCtrl.abort()
                    }
                }, { signal: abortCtrl.signal });
            },
        }),

        writable: new WritableStream({
            write(chunk, controller) {
                try {
                    server.send(chunk)
                    // if (!abortCtrl.signal.aborted) {
                    //     server.send(chunk)
                    // }
                } catch {
                    abortCtrl.abort()
                }
            },
        }),

        close: function () {
            try {
                // server.close()
                if (server.readyState === WS_STATES.OPEN) {
                    server.close()
                }
            } catch {
                abortCtrl.abort()
            }
        },

        response: new Response(null, {
            status: 101,
            webSocket: client,
        }),

        signal: abortCtrl.signal,
    }
}

/**
 * @param {any} header
 * @param {'relayip' | 'relaysocks' | 'direct' | string} mode
 * @param {any} protocolMode
 * @returns {Promise<any>}
 */
async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    // const connectionHandlers = {
    //     'relayip': () => {
    //         const needDirect =
    //             [1].includes(addressType) ||
    //             (useTargetProtocol && [3].includes(addressType)) ||
    //             (!useTargetProtocol && [4].includes(addressType));
    //         return needDirect
    //             ? connect({ hostname: addressRemote, port: portRemote })
    //             : connect({
    //                 hostname: globalSessionConfig.relay.ip,
    //                 port: globalSessionConfig.relay.port || portRemote,
    //             });
    //     },
    //     'relaysocks': () => {
    //         return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
    //     },
    //     'direct': () => {
    //         return connect({ hostname: addressRemote, port: portRemote });
    //     },
    //     'default': () => {
    //         return connect({ hostname: addressRemote, port: portRemote });
    //     },
    // };
    // // const handler = connectionHandlers[mode] || connectionHandlers['default'];
    // // return handler();
    // return (connectionHandlers[mode] || connectionHandlers['default'])();

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
            // return await socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

function matchUuid(extractedID, uuidString) {
    uuidString = uuidString.replaceAll('-', '')
    for (let index = 0; index < 16; index++) {
        const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16)
        if (extractedID[index] !== expected) {
            return false
        }
    }
    return true
}

async function parseProtocolHeaderType0(buffer, wsInterface) {
    const bytes = new Uint8Array(buffer);
    const view = new DataView(buffer);
    const decoder = new TextDecoder();

    // const extractedID = bytes.slice(1, 17)
    const extractedID = bytes.subarray(1, 17)
    if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
        if (wsInterface && wsInterface.close instanceof Function) {
            wsInterface.close(1013, 'Invalid user');
        }
        throw new Error(`Invalid user: UUID does not match`);
    }

    const version = bytes[0];
    const optionsLength = bytes[17];
    const commandIndex = 18 + optionsLength;
    const command = bytes[commandIndex];

    const portIndex = 18 + optionsLength + 1;
    const port = view.getUint16(portIndex);

    let offset = portIndex + 2;
    const addressType = bytes[offset++];

    let hostname = ''
    switch (addressType) {
        case 1: { // IPv4
            // hostname = bytes.slice(offset, offset + 4).join('.');
            // hostname = bytes.subarray(offset, offset + 4).join('.');
            hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
            offset += 4;
            break;
        }
        case 2: { // Domain name
            const domainLen = bytes[offset++];
            // hostname = decoder.decode(bytes.slice(offset, offset + domainLen));
            hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
            offset += domainLen;
            break;
        }
        case 3: { // IPv6
            hostname = view.getUint16(offset).toString(16);
            for (let i = 1; i < 8; i++) {
                hostname += ':' + view.getUint16(offset + i * 2).toString(16);
                // hostname += `:${view.getUint16(offset + i * 2).toString(16)}`;
            }
            offset += 16;
            break;
        }
        case 38: { // IPv6
            for (let i = 0; i < 8; i++) {
                hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                // hostname += `${i ? ':' : ''}${view.getUint16(offset + i * 2).toString(16)}`;
                // hostname += (i > 0 ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                // hostname += `${i > 0 ? ':' : ''}${view.getUint16(offset + i * 2).toString(16)}`;
            }
            offset += 16;
            break;
        }
        default: {
            throw new Error(`Unsupported address type: ${addressType}`);
        }
    }

    // const rawClientData = bytes.slice(offset);
    const rawClientData = bytes.subarray(offset);

    //  wsInterface.send(new Uint8Array([version, 0]).buffer);
    //  wsInterface.send(Uint8Array.from([version, 0]).buffer);
    wsInterface.send(Uint8Array.of(version, 0).buffer);
    return {
        addressType: addressType,
        addressRemote: hostname,
        portRemote: port,
        rawClientData
    };
}

async function parseProtocolHeaderType1(buffer, wsInterface) {
    const bytes = new Uint8Array(buffer);
    const view = new DataView(buffer);
    const decoder = new TextDecoder();

    const crLfIndex = 56;
    // const extractedPassword = decoder.decode(bytes.slice(0, crLfIndex));
    const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
    if (extractedPassword !== globalSessionConfig.user.sha224) {
        wsInterface.close(1013, 'Invalid password');
        throw new Error(`Invalid password`)
    }

    let offset = crLfIndex + 2;
    const command = bytes[offset++];
    const addressType = bytes[offset++];

    let hostname = '';
    switch (addressType) {
        case 1: { // IPv4
            // hostname = bytes.slice(offset, offset + 4).join('.');
            // hostname = bytes.subarray(offset, offset + 4).join('.');
            hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
            offset += 4;
            break;
        }
        case 3: { // Domain name
            const domainLen = bytes[offset++];
            // hostname = decoder.decode(bytes.slice(offset, offset + domainLen));
            hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
            offset += domainLen;
            break;
        }
        case 4: { // IPv6
            hostname = view.getUint16(offset).toString(16);
            for (let i = 1; i < 8; i++) {
                hostname += ':' + view.getUint16(offset + i * 2).toString(16);
                // hostname += `:${view.getUint16(offset + i * 2).toString(16)}`;
            }
            offset += 16;
            break;
        }
        case 48: { // IPv6
            for (let i = 0; i < 8; i++) {
                hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                // hostname += `${i ? ':' : ''}${view.getUint16(offset + i * 2).toString(16)}`;
                // hostname += (i > 0 ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                // hostname += `${i > 0 ? ':' : ''}${view.getUint16(offset + i * 2).toString(16)}`;
            }
            offset += 16;
            break;
        }
        default: {
            throw new Error(`Unsupported address type: ${addressType}`);
        }
    }

    const port = view.getUint16(offset);
    offset += 4;

    // const rawClientData = bytes.slice(offset);
    const rawClientData = bytes.subarray(offset);

    return {
        addressType: addressType,
        addressRemote: hostname,
        portRemote: port,
        rawClientData
    };
}

function decodeBase64Url(encodedString) {
    return Uint8Array.from(atob(encodedString.replaceAll('-', '+').replaceAll('_', '/')), (c) => c.charCodeAt(0)).buffer;
}

async function socks5Connect(addressType, addressRemote, portRemote, targetProtocol) {
    const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
    let socket, reader, writer;
    const encoder = new TextEncoder()
    // Connect to the SOCKS server
    socket = await connect({ hostname, port });
    reader = socket.readable.getReader();
    writer = socket.writable.getWriter();
    if (!reader || !writer) return cleanupResources();
    // Send SOCKS5 greeting
    const socksGreeting = new Uint8Array([5, 2, 0, 2]); // Support No Auth and Username/Password Auth
    await writer.write(socksGreeting);
    // Read server response
    let res = (await reader.read()).value;
    if (res[0] !== 0x05) return cleanupResources();
    if (res[1] === 0xff) return cleanupResources();
    // Handle authentication if required
    if (res[1] === 0x02) {
        if (!username || !password) return cleanupResources();
        const authRequest = new Uint8Array([
            1,
            username.length,
            ...encoder.encode(username),
            password.length,
            ...encoder.encode(password)
        ]);
        await writer.write(authRequest);
        res = (await reader.read()).value;
        if (res[0] !== 0x01 || res[1] !== 0x00) return cleanupResources();
    }
    // Prepare and send request
    let DSTADDR;
    const addressTypeMap = targetProtocol
        ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
        : { IPv4: 1, DOMAIN: 3, IPv6: 4 };
    switch (addressType) {
        case addressTypeMap.IPv4:
            DSTADDR = new Uint8Array([1, ...addressRemote.split('.').map(Number)]);
            break;
        case addressTypeMap.DOMAIN:
            DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
            break;
        case addressTypeMap.IPv6:
            // DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => [parseInt(x.slice(0, 2), 16), parseInt(x.slice(2), 16)])]);
            // DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => x.padStart(4, '0').match(/../g).map(y => parseInt(y, 16)))]);
            DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => x.padStart(4, '0').match(/.{2}/g).map(y => parseInt(y, 16)))]);
            break;
        default:
            return cleanupResources();
    }
    const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
    await writer.write(socksRequest);
    // Read final response
    res = (await reader.read()).value;
    if (res[1] !== 0x00) return cleanupResources();
    reader.releaseLock();
    writer.releaseLock();
    return socket;

    async function cleanupResources() {
        if (reader) { reader?.releaseLock() }
        if (writer) { writer?.releaseLock() }
        // if (socket) { socket.close(); socket = null; }
        if (socket) { try { await socket?.close(); } catch (closeError) { } }
        // if (socket) { try { await socket.close(); } finally { } }
        return new Response('Connect socks5 error', { status: 400 });
    }
}

function socks5AddressParser(address) {
    const [latter, former] = address.split("@").reverse();
    const [hostname, port] = latter.split(":");
    let username, password;
    if (former) {
        const formers = former.split(":");
        if (formers.length !== 2) {
            throw new Error('Invalid SOCKS address format: Expected "username:password" before "@"');
        }
        [username, password] = formers;
    }
    if (Object.is(port, NaN)) {
        throw new Error('Invalid SOCKS address format: Port must be a valid number');
    }
    return { username, password, hostname, port: Number(port) }
}

async function fetchRemoteData(url) {
    const randomVersion = Math.floor(Math.random() * (128 - 110 + 1)) + 110;

    const headers = new Headers({
        'User-Agent': `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${randomVersion}.0.0.0 Safari/537.36`,
    });

    const response = await fetch(url, { headers });
    if (!response.ok) {
        throw new Error('Failed to fetch the input string');
    }
    return response.text();
}

function encryptVar(str) {
    return str.split('').map(char => {
        return (char.charCodeAt(0) << 2).toString(16);
    }).join('-_-');
}

function decryptVar(str) {
    return str.split('-_-').map(hex => {
        return String.fromCharCode(parseInt(hex, 16) >> 2);
    }).join('');
}

const protocolTypes = {
    p0: decryptVar('1d8-_-1b0-_-194-_-1cc-_-1cc'),
    p1: decryptVar('1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8'),
};


function parseAddressLines(inputString) {
    const lines = inputString.trim().split('\n');
    const uniqueEntries = new Set(lines); // Use Set to remove duplicate lines
    const commentCounts = {};

    return Array.from(uniqueEntries).map(line => {
        let atValue, atType = null;
        let processedLine = line;

        // Extract type value if present
        const typeIndex = processedLine.indexOf('|');
        if (typeIndex > -1) {
            const typeValue = processedLine.substring(typeIndex + 1).trim().toLowerCase();
            atType = typeValue || null;
            processedLine = processedLine.slice(0, typeIndex).trim(); // Use the part before '|' for further processing
        }

        // Extract @ value if present
        const atIndex = processedLine.indexOf('@');
        if (atIndex > -1) {
            atValue = processedLine.substring(atIndex + 1).trim();
            processedLine = processedLine.substring(0, atIndex).trim(); // Use the part before '@' for further processing
        }

        const [addressPort, comment] = processedLine.split('#');
        let processedComment = comment ? comment.trim() : 'Unknown';

        commentCounts[processedComment] = (commentCounts[processedComment] || 0) + 1;
        processedComment += `-${commentCounts[processedComment]}`;

        if (addressPort.startsWith('[')) { // Handle IPv6 addresses
            const closingBracketIndex = addressPort.indexOf(']');
            if (closingBracketIndex > -1) {
                const address = addressPort.substring(0, closingBracketIndex + 1);
                const port = addressPort.slice(closingBracketIndex + 2).trim() || null;
                return { address, port: port || null, comment: processedComment, atValue, atType };
            }
        } else { // Handle IPv4 addresses or domain names
            const [address, port] = addressPort.trim().split(':'); // Trim to remove potential leading/trailing spaces
            return { address: address.trim(), port: port ? port.trim() : null, comment: processedComment, atValue, atType };
        }
    });
}

function generateConfigs(inputString, uuid, password, enableTls, domain) {
    const transportProtocol = 'ws';
    const fingerprint = 'chrome';

    // Get path based on protocol and additional parameters
    function getPath(protocol, atValue, atType) {
        const basePath = protocol === protocolTypes.p0
            ? '/?ed=2560'
            : '/trws?ed=2560';

        if (!atValue) return basePath;

        // Add parameters based on different types
        switch (atType?.toLowerCase()) {
            // case 'relayip':
            //     return `${basePath}&relayip=${atValue}`;
            case 'socks':
                return `${basePath}&socks=${atValue}`;
            default:
                return `${basePath}&relayip=${atValue}`;
        }
    }

    const parsedAddresses = parseAddressLines(inputString);
    const configs = {
        protocolP0: [],
        protocolP1: [],
        customP0: { fullConfig: [], namesOnly: [] },
        customP1: { fullConfig: [], namesOnly: [] }
        // customP0: { fullConfig: ['  - ' + JSON.stringify({ name: "Dns-out", type: "dns" })], namesOnly: [] },
        // customP1: { fullConfig: ['  - ' + JSON.stringify({ name: "Dns-out", type: "dns" })], namesOnly: [] }
    };

    // Generate protocol string with path parameters
    function generateProtocolString(protocol, credentials, address, port, comment, atValue, atType) {
        const securityType = enableTls ? 'tls' : 'none';
        const path = getPath(protocol, atValue, atType);
        const baseString = `${protocol}://${credentials}@${address}:${port}?security=${securityType}&sni=${domain}&fp=${fingerprint}&type=${transportProtocol}&host=${domain}&path=${encodeURIComponent(path)}`;
        const encryptionPart = protocol === protocolTypes.p0 ? '&encryption=none' : '';
        return `${baseString}${encryptionPart}#${encodeURIComponent(comment)}`;
    }

    // Generate custom config with path parameters
    function generateCustomConfig(protocol, address, port, comment, atValue, atType) {
        const isFirstProtocol = protocol === protocolTypes.p0;
        const nodeName = `${protocol}-${comment}`;
        const path = getPath(protocol, atValue, atType);

        return {
            name: nodeName,
            type: protocol,
            server: address,
            port: enableTls ? (port || 443) : (port || 80),
            [isFirstProtocol ? 'uuid' : 'password']: isFirstProtocol ? uuid : password,
            udp: true,
            tls: enableTls,
            network: "ws",
            [isFirstProtocol ? 'servername' : 'sni']: domain,
            ...(enableTls ? {
                "skip-cert-verify": false,
                "client-fingerprint": "chrome"
            } : {}),
            "ws-opts": {
                path: path,
                headers: {
                    Host: domain
                }
            }
        };
    }

    parsedAddresses.forEach(({ address, port, comment, atValue, atType }) => {
        const actualPort = enableTls ? (port || 443) : (port || 80);

        // Generate string configs
        const protocolP0 = generateProtocolString(protocolTypes.p0, uuid, address, actualPort, comment, atValue, atType);
        const protocolP1 = generateProtocolString(protocolTypes.p1, password, address, actualPort, comment, atValue, atType);

        configs.protocolP0.push(protocolP0.trim());
        configs.protocolP1.push(protocolP1.trim());

        // Generate custom configs
        const customP0 = generateCustomConfig(protocolTypes.p0, address, actualPort, comment, atValue, atType);
        const customP1 = generateCustomConfig(protocolTypes.p1, address, actualPort, comment, atValue, atType);

        configs.customP0.fullConfig.push('  - ' + JSON.stringify(customP0));
        configs.customP1.fullConfig.push('  - ' + JSON.stringify(customP1));
        configs.customP0.namesOnly.push(`      - "${customP0.name}"`);
        configs.customP1.namesOnly.push(`      - "${customP1.name}"`);
    });

    return {
        protocolP0: configs.protocolP0.join('\n'),
        protocolP1: configs.protocolP1.join('\n'),
        customP0: {
            fullConfig: configs.customP0.fullConfig.join('\n'),
            namesOnly: configs.customP0.namesOnly.join('\n')
        },
        customP1: {
            fullConfig: configs.customP1.fullConfig.join('\n'),
            namesOnly: configs.customP1.namesOnly.join('\n')
        }
    };
}

function getCustomConfig(inputString, uuid, pass, tls, domain, configType, inputTemplate) {
    const configs = generateConfigs(inputString, uuid, pass, tls, domain);
    let proxiesConfig, namesOnly;

    switch (configType.toLowerCase()) {
        case 'p0':
            proxiesConfig = configs.customP0.fullConfig;
            namesOnly = configs.customP0.namesOnly;
            break;
        case 'p1':
            proxiesConfig = configs.customP1.fullConfig;
            namesOnly = configs.customP1.namesOnly;
            break;
        case 'both':
            proxiesConfig = configs.customP0.fullConfig + '\n' + configs.customP1.fullConfig;
            namesOnly = configs.customP0.namesOnly + '\n' + configs.customP1.namesOnly;
            break;
        default:
            throw new Error(`Invalid configType: ${configType}. Supported types are 'p0', 'p1', and 'both'.`);
    }

    inputTemplate = inputTemplate.replace('${proxiesConfig}', proxiesConfig);
    return inputTemplate;
}